#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试SlowOnly特征提取功能
验证推理阶段的特征提取与预处理阶段的一致性
"""

import os
import sys
import numpy as np
from pathlib import Path
import tempfile
import cv2

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'data_process' / 'inference' / 'scripts'))

def create_test_video(output_path: str, duration: int = 5, fps: int = 30, width: int = 224, height: int = 224):
    """创建测试视频"""
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    total_frames = duration * fps
    for i in range(total_frames):
        # 创建彩色渐变帧
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        frame[:, :, 0] = (i * 255 // total_frames) % 256  # 红色通道
        frame[:, :, 1] = ((i * 2) * 255 // total_frames) % 256  # 绿色通道
        frame[:, :, 2] = ((i * 3) * 255 // total_frames) % 256  # 蓝色通道
        out.write(frame)
    
    out.release()
    print(f"✅ 创建测试视频: {output_path}")
    print(f"   视频参数: {duration}秒, {fps}fps, {width}x{height}")

def test_feature_extraction():
    """测试特征提取功能"""
    print("🧪 开始测试SlowOnly特征提取功能...")
    
    # 创建临时测试视频
    with tempfile.TemporaryDirectory() as temp_dir:
        test_video_path = os.path.join(temp_dir, "test_video.mp4")
        create_test_video(test_video_path, duration=3, fps=30)
        
        # 导入推理模块
        try:
            from bmn_inference import extract_slowonly_features
            print("✅ 成功导入推理模块")
        except ImportError as e:
            print(f"❌ 导入推理模块失败: {e}")
            return False
        
        # 测试特征提取
        feature_output_dir = os.path.join(temp_dir, "features")
        print(f"\n📊 测试特征提取...")
        print(f"   输入视频: {test_video_path}")
        print(f"   输出目录: {feature_output_dir}")
        
        try:
            feature_file = extract_slowonly_features(test_video_path, feature_output_dir)
            
            if feature_file and os.path.exists(feature_file):
                print(f"✅ 特征提取成功: {feature_file}")
                
                # 验证特征文件
                features = np.loadtxt(feature_file, delimiter=',', skiprows=1)
                print(f"   特征形状: {features.shape}")
                print(f"   特征范围: [{features.min():.4f}, {features.max():.4f}]")
                print(f"   特征均值: {features.mean():.4f}")
                print(f"   特征标准差: {features.std():.4f}")
                
                # 验证特征不是随机生成的（检查是否有明显的模式）
                if features.shape[0] == 2048:  # SlowOnly R50特征维度
                    print("✅ 特征维度正确 (2048)")
                else:
                    print(f"⚠️  特征维度异常: {features.shape[0]} (期望: 2048)")
                
                # 检查特征是否为零向量（可能表示提取失败）
                if np.allclose(features, 0):
                    print("⚠️  特征全为零，可能提取失败")
                    return False
                else:
                    print("✅ 特征提取正常，非零向量")
                
                return True
            else:
                print("❌ 特征提取失败，未生成特征文件")
                return False
                
        except Exception as e:
            print(f"❌ 特征提取过程中出错: {e}")
            import traceback
            traceback.print_exc()
            return False

def test_feature_consistency():
    """测试特征提取的一致性"""
    print("\n🔄 测试特征提取一致性...")
    
    # 创建临时测试视频
    with tempfile.TemporaryDirectory() as temp_dir:
        test_video_path = os.path.join(temp_dir, "consistency_test.mp4")
        create_test_video(test_video_path, duration=2, fps=30)
        
        # 导入推理模块
        from bmn_inference import extract_slowonly_features
        
        # 多次提取特征
        feature_output_dir = os.path.join(temp_dir, "features")
        features_list = []
        
        for i in range(2):  # 提取两次
            print(f"   第{i+1}次提取...")
            feature_file = extract_slowonly_features(test_video_path, feature_output_dir)
            
            if feature_file and os.path.exists(feature_file):
                features = np.loadtxt(feature_file, delimiter=',', skiprows=1)
                features_list.append(features)
                # 删除特征文件以便下次重新提取
                os.remove(feature_file)
            else:
                print(f"❌ 第{i+1}次特征提取失败")
                return False
        
        if len(features_list) == 2:
            # 比较两次提取的特征
            diff = np.abs(features_list[0] - features_list[1])
            max_diff = diff.max()
            mean_diff = diff.mean()
            
            print(f"   特征差异统计:")
            print(f"   最大差异: {max_diff:.6f}")
            print(f"   平均差异: {mean_diff:.6f}")
            
            # 由于模型是确定性的，相同输入应该产生相同输出
            if max_diff < 1e-5:
                print("✅ 特征提取一致性良好")
                return True
            else:
                print("⚠️  特征提取存在差异，可能存在随机性")
                return False
        else:
            print("❌ 无法进行一致性测试")
            return False

def main():
    """主测试函数"""
    print("🚀 SlowOnly特征提取测试")
    print("=" * 50)
    
    # 检查环境
    print("🔍 检查测试环境...")
    
    # 检查CUDA
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✅ CUDA可用: {torch.cuda.get_device_name(0)}")
        else:
            print("⚠️  CUDA不可用，将使用CPU")
    except ImportError:
        print("❌ PyTorch未安装")
        return
    
    # 检查MMAction2
    try:
        import mmaction
        print(f"✅ MMAction2: {mmaction.__version__}")
    except ImportError:
        print("❌ MMAction2未安装")
        return
    
    # 运行测试
    test_results = []
    
    # 测试1: 基本特征提取功能
    print("\n" + "="*50)
    result1 = test_feature_extraction()
    test_results.append(("基本特征提取", result1))
    
    # 测试2: 特征提取一致性
    print("\n" + "="*50)
    result2 = test_feature_consistency()
    test_results.append(("特征提取一致性", result2))
    
    # 输出测试结果
    print("\n" + "="*50)
    print("📋 测试结果汇总:")
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    all_passed = all(result for _, result in test_results)
    if all_passed:
        print("\n🎉 所有测试通过！SlowOnly特征提取功能正常")
    else:
        print("\n⚠️  部分测试失败，请检查配置和环境")

if __name__ == "__main__":
    main()
